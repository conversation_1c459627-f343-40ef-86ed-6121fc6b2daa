# Style Selector Feature

## Overview

The Style Selector feature allows users to dynamically change the visual appearance of map layers by selecting from available styles configured in GeoServer. This feature is integrated into the legend panel and provides an intuitive way to switch between different layer visualizations.

## Features

- **Automatic Style Detection**: Fetches available styles for each layer from GeoServer REST API
- **Dynamic Style Switching**: Changes layer appearance in real-time without page reload
- **Integrated UI**: Style selector is embedded within each legend item
- **Responsive Design**: Works on both desktop and mobile devices
- **Error Handling**: Gracefully handles cases where styles are not available

## How It Works

### 1. Style Information Extraction

When layers are loaded from GeoServer, the system automatically:
- Extracts available styles directly from the WMS GetCapabilities XML response
- Parses `<Style>` elements within each `<Layer>` element to get style names and metadata
- Stores style information in the layer's `styleInfo` property during initial layer loading
- No additional API calls required - all style information comes from the capabilities response

### 2. User Interface

For each visible layer in the legend panel:
- If multiple styles are available, a dropdown selector appears
- The dropdown shows style names (or titles if available)
- The current active style is pre-selected
- Tooltips show style descriptions when available

### 3. Style Application

When a user selects a different style:
- The WMS layer is recreated with the new `STYLES` parameter
- The legend URL is updated with the `&STYLE={styleName}` parameter
- The change is applied immediately to both the map and legend
- No server-side configuration changes required

## Technical Implementation

### Key Components Modified

1. **Types (`src/types.ts`)**:
   - Added `LayerStyle` interface for style information
   - Added `StyleInfo` interface for managing style state
   - Extended `GeoLayer` and `LegendItem` interfaces

2. **GeoServer Composable (`src/composables/useGeoServer.ts`)**:
   - Added `extractStylesFromLayer()` function to parse styles from XML
   - Enhanced layer creation to include style information from capabilities response
   - Improved XML parsing to extract style names, titles, and abstracts

3. **Layers Composable (`src/composables/useLayers.ts`)**:
   - Added `changeLayerStyle()` function
   - Added `getLayerStyles()` and `getCurrentLayerStyle()` functions
   - Integrated style management into layer lifecycle

4. **Map Composable (`src/composables/useMap.ts`)**:
   - Added `updateLayerStyle()` function
   - Implemented WMS layer recreation with new style parameters

5. **Legend Components**:
   - **LegendPanel.vue**: Passes style management functions to legend items
   - **LegendItem.vue**: Displays style selector and handles style changes

### Configuration

The feature uses the following configuration from `src/config.ts`:
- `getGeoServerCapabilitiesUrl()`: WMS GetCapabilities endpoint for fetching layer and style information
- `config.server.geoserverWmsPath`: Base WMS URL for layer rendering and legend generation

## Usage

### For End Users

1. **Open the Legend Panel**: Click the legend button to open the legend panel
2. **Find Layers with Multiple Styles**: Look for layers that have a "Estilo:" dropdown
3. **Select a Style**: Click the dropdown and choose a different style
4. **View Changes**: The map and legend will update immediately

### For Developers

#### Adding New Style Support

To add support for additional style formats or custom style parsing:

```typescript
// In useGeoServer.ts
const extractStylesFromLayer = (layerElement: Element | null, layerName: string): StyleInfo => {
  // Custom logic for parsing styles from GetCapabilities XML
  // Can be extended to support different XML structures or additional metadata
};
```

#### Customizing Style Display

To customize how styles are displayed in the UI:

```vue
<!-- In LegendItem.vue -->
<select class="style-selector" @change="handleStyleChange">
  <option v-for="style in availableStyles" :value="style.name">
    {{ style.title || style.name }}
  </option>
</select>
```

## API Endpoints Used

- **WMS GetCapabilities** - Fetches layer information including available styles from XML response
- **WMS** with `STYLES` parameter - Applies selected style to map layer
- **GetLegendGraphic** with `STYLE` parameter - Updates legend image for specific style

## Error Handling

The feature includes comprehensive error handling:
- XML parsing errors when extracting styles from GetCapabilities
- Missing or invalid style information in layer definitions
- Network errors during GetCapabilities requests
- Graceful fallback to default styles when style information is unavailable

## Browser Compatibility

- Modern browsers with ES2020 support
- Responsive design for mobile devices
- Accessible UI with proper ARIA labels

## Future Enhancements

Potential improvements for future versions:
- Style preview thumbnails
- Custom style upload functionality
- Style categorization and filtering
- Batch style application to multiple layers
- Style favorites and user preferences
