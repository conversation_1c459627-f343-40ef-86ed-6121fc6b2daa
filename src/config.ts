/**
 * Application configuration loaded from environment variables
 */

function parseEnvValue<T>(value: string | undefined, defaultValue: T): T {
    if (value === undefined) return defaultValue;

    if (typeof defaultValue === 'number') {
        const parsed = parseFloat(value);
        return (isNaN(parsed) ? defaultValue : parsed) as T;
    }

    if (typeof defaultValue === 'boolean') {
        return (value === 'true') as T;
    }

    return value as T;
}

export const config = {
    app: {
        title: parseEnvValue(import.meta.env.VITE_APP_TITLE, "Geoportal Tabacay Aguilán"),
        subtitle: parseEnvValue(import.meta.env.VITE_APP_SUBTITLE, "Microcuenca de los Ríos Tabacay y Aguilán - Azogues"),
        name: parseEnvValue(import.meta.env.VITE_APP_NAME, "geoportal-tabacay-aguilan"),
    },
    server: {
        geoserverProxyTarget: parseEnvValue(import.meta.env.VITE_GEOSERVER_PROXY_TARGET, "http://localhost:8080"),
        geoserverWmsPath: parseEnvValue(import.meta.env.VEITE_GEOSERVER_WMS_PATH, "/geoserver/wms"),
        geoserverWorkspace: parseEnvValue(import.meta.env.VITE_GEOSERVER_WORKSPACE, "geoportal_ws"),
        nominatimApiUrl: parseEnvValue(import.meta.env.VITE_NOMINATIM_API_URL, "https://nominatim.openstreetmap.org/search"),
    },
    institutional: {
        ucacue: {
            name: parseEnvValue(import.meta.env.VITE_UCACUE_NAME, "UCACUE - Universidad Católica de Cuenca"),
            url: parseEnvValue(import.meta.env.VITE_UCACUE_URL, "https://www.ucacue.edu.ec/"),
        },
        emapal: {
            name: parseEnvValue(import.meta.env.VITE_EMAPAL_NAME, "EMAPAL EP - Empresa Pública Municipal"),
            url: parseEnvValue(import.meta.env.VITE_EMAPAL_URL, "https://www.emapal.gob.ec/"),
        },
    },
    assets: {
        ucacueLogo: parseEnvValue(import.meta.env.VITE_UCACUE_LOGO, "/logo-u.png"),
        emapalLogo: parseEnvValue(import.meta.env.VITE_EMAPAL_LOGO, "/Emapal.png"),
        favicon: parseEnvValue(import.meta.env.VITE_FAVICON, "/geoportal-icon.svg"),
    },
    footer: {
        projectText: parseEnvValue(import.meta.env.VITE_FOOTER_PROJECT_TEXT, 'Geoportal para el proyecto "Desarrollo del plan de manejo para la microcuenca de los ríos Tabacay y Aguilán, en la ciudad de Azogues"'),
        rightsText: parseEnvValue(import.meta.env.VITE_FOOTER_RIGHTS_TEXT, "Todos los derechos reservados"),
    },
    map: {
        defaultCenter: {
            lat: parseEnvValue(import.meta.env.VITE_MAP_DEFAULT_CENTER_LAT, -2.8833),
            lng: parseEnvValue(import.meta.env.VITE_MAP_DEFAULT_CENTER_LNG, -78.9833),
        },
        defaultZoom: parseEnvValue(import.meta.env.VITE_MAP_DEFAULT_ZOOM, 10),
        minZoom: parseEnvValue(import.meta.env.VITE_MAP_MIN_ZOOM, 6),
        maxBoundsViscosity: parseEnvValue(import.meta.env.VITE_MAP_MAX_BOUNDS_VISCOSITY, 0.8),
        ecuadorBounds: {
            southwest: {
                lat: parseEnvValue(import.meta.env.VITE_MAP_ECUADOR_BOUNDS_SW_LAT, -5.0159314),
                lng: parseEnvValue(import.meta.env.VITE_MAP_ECUADOR_BOUNDS_SW_LNG, -92.2072392),
            },
            northeast: {
                lat: parseEnvValue(import.meta.env.VITE_MAP_ECUADOR_BOUNDS_NE_LAT, 1.8835964),
                lng: parseEnvValue(import.meta.env.VITE_MAP_ECUADOR_BOUNDS_NE_LNG, -75.192504),
            },
        },
        homeBounds: {
            southwest: {
                lat: parseEnvValue(import.meta.env.VITE_MAP_HOME_BOUNDS_SW_LAT, -2.78349),
                lng: parseEnvValue(import.meta.env.VITE_MAP_HOME_BOUNDS_SW_LNG, -79.03931),
            },
            northeast: {
                lat: parseEnvValue(import.meta.env.VITE_MAP_HOME_BOUNDS_NE_LAT, -2.48400),
                lng: parseEnvValue(import.meta.env.VITE_MAP_HOME_BOUNDS_NE_LNG, -78.59242),
            },
        },
    },
    cdn: {
        leafletCss: {
            url: parseEnvValue(import.meta.env.VITE_LEAFLET_CSS_URL, "https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"),
            integrity: parseEnvValue(import.meta.env.VITE_LEAFLET_CSS_INTEGRITY, "sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="),
        },
        fontAwesome: {
            url: parseEnvValue(import.meta.env.VITE_FONTAWESOME_CSS_URL, "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"),
        },
    },
    search: {
        minQueryLength: parseEnvValue(import.meta.env.VITE_SEARCH_MIN_QUERY_LENGTH, 2),
        debounceMs: parseEnvValue(import.meta.env.VITE_SEARCH_DEBOUNCE_MS, 500),
    },
    layer: {
        defaultOpacity: parseEnvValue(import.meta.env.VITE_LAYER_DEFAULT_OPACITY, 0.6),
        defaultZIndex: parseEnvValue(import.meta.env.VITE_LAYER_DEFAULT_ZINDEX, 10),
    },
} as const;

export function getGeoServerCapabilitiesUrl(): string {
  return getWorkspaceWmsUrl() + "?SERVICE=WMS&VERSION=1.3.0&REQUEST=GetCapabilities";
}

export function getWorkspaceWmsUrl(): string {
  const workspacePath = `/geoserver/${config.server.geoserverWorkspace}/wms`;

  if (import.meta.env.DEV) {
    return workspacePath;
  }
  return `${config.server.geoserverProxyTarget}${workspacePath}`;
}

export function getWorkspaceWfsUrl(): string {
  const workspacePath = `/geoserver/${config.server.geoserverWorkspace}/wfs`;

  if (import.meta.env.DEV) {
    return workspacePath;
  }
  return `${config.server.geoserverProxyTarget}${workspacePath}`;
}

export function getNominatimSearchUrl(query: string): string {
  return `${config.server.nominatimApiUrl}?format=json&q=${encodeURIComponent(query)}`;
}