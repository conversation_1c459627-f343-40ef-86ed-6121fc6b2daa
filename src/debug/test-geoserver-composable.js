/**
 * Test script to verify useGeoServer composable functionality
 * This can be run in the browser console to test the reactive system
 */

console.log('Testing useGeoServer composable...');

// Test function to verify GeoServer composable reactivity
function testGeoServerComposable() {
  console.log('=== Testing useGeoServer Composable ===');

  if (!window.layerManager) {
    console.error('LayerManager not available. Make sure the app is loaded.');
    return;
  }
  
  // Access the GeoServer composable state through the layer manager
  const layerManager = window.layerManager;
  
  // Test 1: Check reactive state
  console.log('isLoading:', layerManager.isLoading.value);
  console.log('error:', layerManager.error.value);
  console.log('hasCachedData:', layerManager.hasCachedData.value);
  console.log('cacheAge:', layerManager.cacheAge.value);
  
  // Test 2: Force a refresh and observe state changes
  async function testRefresh() {
    console.log('Clearing cache and forcing refresh...');
    layerManager.clearCache();
    
    console.log('Cache cleared. State:');
    console.log('hasCachedData:', layerManager.hasCachedData.value);
    
    console.log('Initializing layers...');
    await layerManager.initialize();
    
    console.log('Layers initialized. State:');
    console.log('isLoading:', layerManager.isLoading.value);
    console.log('error:', layerManager.error.value);
    console.log('hasCachedData:', layerManager.hasCachedData.value);
    console.log('cacheAge:', layerManager.cacheAge.value);
  }
  
  // Run the refresh test
  testRefresh().catch(err => {
    console.error('Error during refresh test:', err);
  });
}

// Run the test
testGeoServerComposable();

// Instructions for manual testing in browser console
console.log(`
To test the GeoServer composable manually:

1. Access the layer manager: 
   const layerManager = window.layerManager;

2. Check reactive state:
   layerManager.isLoading.value
   layerManager.error.value
   layerManager.hasCachedData.value
   layerManager.cacheAge.value

3. Clear cache and force refresh:
   layerManager.clearCache();
   layerManager.initialize();

4. Observe state changes in the Vue DevTools
`);
