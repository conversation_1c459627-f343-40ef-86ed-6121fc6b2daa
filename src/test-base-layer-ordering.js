// Test script to verify base layer ordering fix
// This can be run in the browser console to test that base layers stay at the bottom

console.log('Testing base layer ordering fix...');

/**
 * Test function to verify that base layers stay at the bottom when changed
 */
function testBaseLayerOrdering() {
  console.log('=== Testing Base Layer Ordering Fix ===');

  // Check if map composable is available
  if (!window.mapComposable) {
    console.error('Map composable not available. Make sure the app is loaded and mapComposable is exposed on window.');
    return;
  }

  const mapComposable = window.mapComposable;
  const map = mapComposable.getMap();

  if (!map) {
    console.error('Map instance not available.');
    return;
  }

  console.log('Map instance found:', map);
  console.log('Current base layer:', mapComposable.currentBaseLayer.value);
  console.log('Available layers:', mapComposable.layers.value.length);

  // Test 1: Add some test overlay layers if none exist
  if (mapComposable.layers.value.length === 0) {
    console.log('No overlay layers found. The test will focus on base layer changes only.');
  }

  // Test 2: Change base layer and verify ordering
  const availableBaseLayers = ['osm', 'satellite', 'topo'];
  const currentBaseLayer = mapComposable.currentBaseLayer.value;
  
  console.log(`Current base layer: ${currentBaseLayer}`);
  
  // Find a different base layer to switch to
  const targetBaseLayer = availableBaseLayers.find(layer => layer !== currentBaseLayer);
  
  if (!targetBaseLayer) {
    console.error('No alternative base layer found for testing.');
    return;
  }

  console.log(`Switching from ${currentBaseLayer} to ${targetBaseLayer}...`);
  
  // Record layer order before change
  const layersBefore = [];
  map.eachLayer((layer) => {
    layersBefore.push({
      layer: layer,
      zIndex: layer.options?.zIndex || 'undefined',
      type: layer.options?.layers ? 'overlay' : 'base'
    });
  });
  
  console.log('Layers before base layer change:', layersBefore.length);
  layersBefore.forEach((item, index) => {
    console.log(`  ${index}: ${item.type} layer, zIndex: ${item.zIndex}`);
  });

  // Change base layer
  mapComposable.changeBaseLayer(targetBaseLayer);
  
  // Wait a moment for the change to take effect
  setTimeout(() => {
    console.log(`Base layer changed to: ${mapComposable.currentBaseLayer.value}`);
    
    // Record layer order after change
    const layersAfter = [];
    map.eachLayer((layer) => {
      layersAfter.push({
        layer: layer,
        zIndex: layer.options?.zIndex || 'undefined',
        type: layer.options?.layers ? 'overlay' : 'base'
      });
    });
    
    console.log('Layers after base layer change:', layersAfter.length);
    layersAfter.forEach((item, index) => {
      console.log(`  ${index}: ${item.type} layer, zIndex: ${item.zIndex}`);
    });

    // Test 3: Verify base layer is at the bottom
    let baseLayerFound = false;
    let overlayLayersAfterBase = 0;
    
    for (let i = 0; i < layersAfter.length; i++) {
      const item = layersAfter[i];
      if (item.type === 'base') {
        baseLayerFound = true;
        // Count overlay layers that come after this base layer
        for (let j = i + 1; j < layersAfter.length; j++) {
          if (layersAfter[j].type === 'overlay') {
            overlayLayersAfterBase++;
          }
        }
        break;
      }
    }

    if (baseLayerFound && overlayLayersAfterBase === 0) {
      console.log('✅ SUCCESS: Base layer is properly positioned at the bottom!');
    } else if (baseLayerFound && overlayLayersAfterBase > 0) {
      console.log(`❌ ISSUE: Base layer found but ${overlayLayersAfterBase} overlay layers are below it.`);
    } else {
      console.log('❌ ISSUE: Base layer not found in layer stack.');
    }

    // Test 4: Change back to original base layer
    console.log(`Changing back to original base layer: ${currentBaseLayer}...`);
    mapComposable.changeBaseLayer(currentBaseLayer);
    
    setTimeout(() => {
      console.log(`Base layer restored to: ${mapComposable.currentBaseLayer.value}`);
      console.log('=== Base Layer Ordering Test Complete ===');
    }, 100);
    
  }, 100);
}

/**
 * Test function to verify layer ordering with multiple overlay layers
 */
function testLayerOrderingWithOverlays() {
  console.log('=== Testing Layer Ordering with Overlays ===');

  if (!window.mapComposable || !window.layerManager) {
    console.error('Map composable or layer manager not available.');
    return;
  }

  const mapComposable = window.mapComposable;
  const layerManager = window.layerManager;

  // Get some overlay layers
  const overlayLayers = layerManager.layers.value.filter(layer => !layer.visible);
  
  if (overlayLayers.length < 2) {
    console.log('Not enough overlay layers available for comprehensive testing.');
    return;
  }

  console.log(`Found ${overlayLayers.length} overlay layers for testing.`);

  // Make a couple of layers visible
  const testLayers = overlayLayers.slice(0, 2);
  testLayers.forEach(layer => {
    console.log(`Making layer visible: ${layer.name}`);
    layerManager.toggleLayerVisibility(layer.id, true);
  });

  // Wait for layers to be added
  setTimeout(() => {
    // Now change base layer and verify ordering
    const currentBaseLayer = mapComposable.currentBaseLayer.value;
    const targetBaseLayer = currentBaseLayer === 'osm' ? 'satellite' : 'osm';
    
    console.log(`Changing base layer from ${currentBaseLayer} to ${targetBaseLayer} with overlays visible...`);
    mapComposable.changeBaseLayer(targetBaseLayer);
    
    setTimeout(() => {
      console.log('Verifying layer order after base layer change with overlays...');
      
      const map = mapComposable.getMap();
      const allLayers = [];
      map.eachLayer((layer) => {
        allLayers.push({
          layer: layer,
          type: layer.options?.layers ? 'overlay' : 'base'
        });
      });
      
      // Check if base layer is at the bottom
      const baseLayerIndex = allLayers.findIndex(item => item.type === 'base');
      const overlaysBeforeBase = allLayers.slice(0, baseLayerIndex).filter(item => item.type === 'overlay').length;
      
      if (overlaysBeforeBase === 0) {
        console.log('✅ SUCCESS: Base layer is at the bottom with overlays on top!');
      } else {
        console.log(`❌ ISSUE: ${overlaysBeforeBase} overlay layers are below the base layer.`);
      }

      // Clean up - hide the test layers
      testLayers.forEach(layer => {
        layerManager.toggleLayerVisibility(layer.id, false);
      });
      
      console.log('=== Layer Ordering with Overlays Test Complete ===');
    }, 200);
  }, 500);
}

// Export test functions to global scope
window.testBaseLayerOrdering = testBaseLayerOrdering;
window.testLayerOrderingWithOverlays = testLayerOrderingWithOverlays;

console.log('Base layer ordering test functions available:');
console.log('- window.testBaseLayerOrdering() - Test basic base layer ordering');
console.log('- window.testLayerOrderingWithOverlays() - Test ordering with overlay layers');
console.log('Run these functions in the browser console to test the base layer ordering fix.');
