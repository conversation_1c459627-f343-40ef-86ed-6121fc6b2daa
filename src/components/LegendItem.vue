<script setup lang="ts">
import {LegendItem} from "../types.ts";
import {computed, ref} from "vue";

// Props
const props = defineProps<{
  legendItem: LegendItem;
  onImageError : (legendItemId: string) => void;
  onImageLoad : (legendItemId: string) => void;
  onImageLoadStart : (legendItemId: string) => void;
  updateOpacity : (legendItemId: string, event: Event) => void;
  updateOpacityFromInput : (legendItemId: string, event: Event) => void;
  changeLayerStyle?: (layerId: string, styleName: string) => void;
  getLayerStyles?: (layerId: string) => any[];
  getCurrentLayerStyle?: (layerId: string) => string;
}>();

// Event handlers
const handleCollapseClick = () => {collapsed.value = !collapsed.value;
};

const collapsed = ref(props.legendItem.collapsed);

// Style management
const availableStyles = computed(() => {
  if (props.getLayerStyles) {
    return props.getLayerStyles(props.legendItem.id);
  }
  return [];
});

const currentStyle = computed(() => {
  if (props.getCurrentLayerStyle) {
    return props.getCurrentLayerStyle(props.legendItem.id);
  }
  return 'default';
});

const hasMultipleStyles = computed(() => {
  return availableStyles.value.length > 1;
});

const handleStyleChange = (event: Event) => {
  const target = event.target as HTMLSelectElement;
  const newStyle = target.value;
  if (props.changeLayerStyle && newStyle) {
    props.changeLayerStyle(props.legendItem.id, newStyle);
  }
};

</script>

<template>
  <div class="legend-item">
    <div
        class="item-header"
        @click="handleCollapseClick"
    >
      <button
          class="collapse-button"
          :aria-label="collapsed ? 'Expandir leyenda' : 'Colapsar leyenda'"
          :aria-expanded="!collapsed"
      >
        <i
            class="fas"
            :class="collapsed ? 'fa-chevron-right' : 'fa-chevron-down'"
        ></i>
      </button>

      <span class="layer-name" :title="legendItem.name">{{ legendItem.name }}</span>
    </div>

    <!-- Opacity controls -->
    <div class="legend-controls">
      <div class="opacity-slider-container">
        <i class="fas fa-eye opacity-slider-icon faded"></i>
        <input
            type="range"
            min="0"
            max="1"
            step="0.01"
            :value="legendItem.opacity"
            @input="props.updateOpacity(legendItem.id, $event)"
            class="opacity-slider"
            :aria-label="`Opacity slider for ${legendItem.name}`"
        />
        <i class="fas fa-eye opacity-slider-icon"></i>
      </div>

      <div class="opacity-input-container">
        <span>Opacidad:</span>
        <input
            type="number"
            min="0"
            max="100"
            :value="Math.round(legendItem.opacity * 100)"
            @input="props.updateOpacityFromInput(legendItem.id, $event)"
            class="opacity-input"
            :aria-label="`Opacity input for ${legendItem.name}`"
        />
        <span>%</span>
      </div>

      <!-- Style selector -->
      <div v-if="hasMultipleStyles" class="style-selector-container">
        <span>Estilo:</span>
        <select
            :value="currentStyle"
            @change="handleStyleChange"
            class="style-selector"
            :aria-label="`Style selector for ${legendItem.name}`"
        >
          <option
              v-for="style in availableStyles"
              :key="style.name"
              :value="style.name"
              :title="style.abstract || style.title"
          >
            {{ style.title || style.name }}
          </option>
        </select>
      </div>
    </div>

    <div
        v-if="!collapsed"
        class="layer-legend"
    >
      <div class="legend-image-container">
        <div class="legend-image-wrapper">
          <!-- Loading state -->
          <div v-if="legendItem.legend_url && legendItem.legendState.loading" class="legend-placeholder">
            <i class="fas fa-spinner fa-spin"></i>
            <span>Loading legend...</span>
          </div>

          <!-- Error state -->
          <div v-else-if="legendItem.legend_url && legendItem.legendState.error" class="legend-placeholder">
            <i class="fas fa-exclamation-triangle"></i>
            <span>Legend not available</span>
          </div>

          <!-- No legend URL -->
          <div v-else-if="!legendItem.legend_url" class="legend-placeholder">
            <i class="fas fa-image"></i>
            <span>No legend available</span>
          </div>

          <!-- Successful image load -->
          <img
              v-else
              :src="legendItem.legend_url"
              :alt="`Leyenda de ${legendItem.name}`"
              class="legend-image"
              :class="{ 'loaded': legendItem.legendState.loaded }"
              @error="props.onImageError(legendItem.id)"
              @load="props.onImageLoad(legendItem.id)"
              @loadstart="props.onImageLoadStart(legendItem.id)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.legend-item {
  border-left: 1px solid #e9ecef;
  margin-left: 4px;
  padding: 0;
}

.item-header {
  display: flex;
  align-items: center;
  padding: 6px 8px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
  cursor: pointer;
  transition: background-color 0.2s ease;
  flex: 1;
}

.item-header:hover {
  background-color: #e9ecef;
}

.collapse-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px;
  margin-right: 6px;
  border-radius: 2px;
  color: #6c757d;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
}

.collapse-button:hover {
  color: #495057;
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(1.1);
}

.collapse-button:focus {
  outline: 2px solid #007bff;
  outline-offset: 1px;
}

.group-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  flex: 1;
  font-weight: 500;
  color: #495057;
}

.layer-name {
  font-size: 13px;
  user-select: none;
  line-height: 1.3;
}

.layer-legend {
  background-color: #ffffff;
}

.legend-layer-name {
  margin: 0 0 6px 0;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
  user-select: none;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.legend-image-container {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  gap: 10px;
  margin-bottom: 10px;
}

.legend-image-wrapper {
  flex-shrink: 0;
}

.legend-image {
  height: auto;
  max-width: 100%;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.3s ease;
  object-fit: contain;
}

.legend-image.loaded {
  opacity: 1;
}

.legend-controls {
  padding-top: 1rem;
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.opacity-slider-container {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  gap: 8px;
  padding: 0 4px 0 4px;
}

.opacity-slider-icon {
  color: #6c757d;
  font-size: 14px;
}

.opacity-slider-icon.faded {
  opacity: 0.3;
}

.opacity-slider {
  flex: 1;
  height: 5px;
  -webkit-appearance: none;
  appearance: none;
  background: #dee2e6;
  outline: none;
  border-radius: 3px;
  cursor: pointer;
}

.opacity-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
}

.opacity-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: #007bff;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.opacity-input-container {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #6c757d;
  padding-bottom: 8px;
}

.opacity-input {
  width: 60px;
  height: 24px;
  padding: 2px 5px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
  text-align: center;
}

.opacity-input:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.style-selector-container {
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 12px;
  color: #6c757d;
  padding-bottom: 8px;
}

.style-selector {
  flex: 1;
  height: 24px;
  padding: 2px 5px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 12px;
  background-color: white;
  cursor: pointer;
}

.style-selector:focus {
  outline: none;
  border-color: #80bdff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.style-selector:hover {
  border-color: #adb5bd;
}

.legend-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  color: #6c757d;
  font-size: 12px;
  text-align: center;
  background-color: #f8f9fa;
  border: 1px dashed #dee2e6;
  border-radius: 4px;
  min-height: 60px;
  max-width: 120px;
}

/* Responsive design */
@media (max-width: 480px) {
  .item-header {
    padding: 4px 6px;
  }

  .layer-name {
    font-size: 12px;
  }

  .collapse-button {
    width: 16px;
    height: 16px;
    margin-right: 4px;
  }

  .legend-layer-name {
    font-size: 12px;
  }
}
</style>