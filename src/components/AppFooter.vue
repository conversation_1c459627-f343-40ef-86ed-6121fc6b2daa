<template>
  <footer class="geoportal-footer">
    <div class="footer-content">
      <p class="footer-project-text">
        {{ footerProjectText }}
      </p>
      <p class="footer-rights">
        {{ footerRightsText }}
        <a 
          :href="ucacueUrl" 
          target="_blank" 
          rel="noopener noreferrer" 
          class="footer-link"
        >
          UCACUE
        </a>
        x
        <a 
          :href="emapalUrl" 
          target="_blank" 
          rel="noopener noreferrer" 
          class="footer-link"
        >
          EMAPAL EP
        </a>
      </p>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { computed } from 'vue';

const footerProjectText = computed(() => import.meta.env.VITE_FOOTER_PROJECT_TEXT);
const footerRightsText = computed(() => import.meta.env.VITE_FOOTER_RIGHTS_TEXT);
const ucacueUrl = computed(() => import.meta.env.VITE_UCACUE_URL);
const emapalUrl = computed(() => import.meta.env.VITE_EMAPAL_URL);
</script>

<style scoped>
.geoportal-footer {
  background-color: #343a40;
  color: white;
  padding: 15px 20px;
  text-align: center;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  position: relative;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-project-text {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 500;
  color: white;
}

.footer-rights {
  margin: 0;
  font-size: 12px;
  color: #adb5bd;
}

.footer-link {
  color: #84a5ff;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.footer-link:hover {
  color: #ff4f4f;
  text-decoration: underline;
}

.footer-link:focus {
  outline: 2px solid #ffffff;
  outline-offset: 2px;
  border-radius: 2px;
}

/* Responsive design */
@media (max-width: 768px) {
  .geoportal-footer {
    padding: 12px 15px;
  }
  
  .footer-project-text {
    font-size: 13px;
    margin-bottom: 6px;
  }
  
  .footer-rights {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .geoportal-footer {
    padding: 10px 15px;
  }
  
  .footer-project-text {
    font-size: 12px;
    line-height: 1.4;
  }
  
  .footer-rights {
    font-size: 10px;
    line-height: 1.3;
  }
}
</style>
