<template>
  <div id="map-container">
    <div id="map" ref="mapElement"></div>
    <FloatingMenu 
      @toggle-layers="toggleLayersPanel"
      @toggle-legend="toggleLegendPanel"
      @toggle-search="toggleSearchPanel"
      @zoom-home="zoomToHome"
    />
    <SearchPanel 
      v-model:visible="searchPanelVisible"
      @close="searchPanelVisible = false"
    />
    <LayersPanel 
      v-model:visible="layersPanelVisible"
      @close="layersPanelVisible = false"
    />
    <LegendPanel 
      v-model:visible="legendPanelVisible"
      @close="legendPanelVisible = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, provide } from 'vue';
import { useMap } from '../composables/useMap';
import { useLayers } from '../composables/useLayers';
import FloatingMenu from './FloatingMenu.vue';
import SearchPanel from './SearchPanel.vue';
import LayersPanel from './LayersPanel.vue';
import LegendPanel from './LegendPanel.vue';

// Reactive state for panel visibility
const searchPanelVisible = ref(false);
const layersPanelVisible = ref(false);
const legendPanelVisible = ref(false);

// Map instance
const mapElement = ref<HTMLElement>();

// Initialize map composable
const mapComposable = useMap();

// Layer manager will be initialized after map is created
let layerManager: any = null;

// Panel toggle functions
const toggleSearchPanel = () => {
  searchPanelVisible.value = !searchPanelVisible.value;
};

const toggleLayersPanel = () => {
  layersPanelVisible.value = !layersPanelVisible.value;
};

const toggleLegendPanel = () => {
  legendPanelVisible.value = !legendPanelVisible.value;
};

const zoomToHome = () => {
  mapComposable.zoomToHome();
};

// Provide services and data to child components
provide('mapManager', () => mapComposable);
provide('layerManager', () => layerManager);

onMounted(async () => {
  if (mapElement.value) {
    // Initialize map
    await mapComposable.initializeMap('map');

    // Initialize layer manager with the map composable instance
    layerManager = useLayers(mapComposable);

    // Initialize layers
    await layerManager.initialize();

    // Make map composable globally available for debugging
    (window as any).mapComposable = mapComposable;
    (window as any).layerManager = layerManager;
  }
});

onUnmounted(() => {
  // Cleanup is handled automatically by the composables
});
</script>

<style scoped>
#map-container {
  flex: 1;
  position: relative;
  height: 100%;
  min-height: 400px;
}

#map {
  width: 100%;
  height: 100%;
}

@media (max-width: 768px) {
  #map-container {
    min-height: 300px;
  }
}
</style>
