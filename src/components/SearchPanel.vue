<template>
  <div 
    v-if="visible" 
    ref="panelRef"
    class="search-panel"
    :class="{ dragging: isDragging }"
  >
    <div class="search-header">
      <div
        class="drag-handle"
        @mousedown="startDrag"
        @touchstart="startDragTouch"
        title="Drag to move panel"
      >
        <i class="fas fa-grip-vertical"></i>
      </div>
      <input
        ref="searchInputRef"
        v-model="searchQuery"
        type="text"
        placeholder="Busca una ubicación..."
        class="search-input"
        @focus="onInputFocus"
      />
      <button
        class="close-button"
        @click="$emit('close')"
        :aria-label="'Close search panel'"
      >
        <i class="fas fa-times"></i>
      </button>
    </div>
    <div class="search-results">
      <div v-if="isLoading" class="loading">Searching...</div>
      <div v-else-if="searchResults.length === 0 && searchQuery.length > 2" class="no-results">
        No results found
      </div>
      <div v-else-if="error" class="error">{{ error }}</div>
      <div 
        v-for="result in searchResults" 
        :key="result.place_id"
        class="search-result-item"
        @click="selectResult(result)"
        @keydown.enter="selectResult(result)"
        @keydown.space.prevent="selectResult(result)"
        tabindex="0"
        role="button"
        :aria-label="`Go to ${result.display_name}`"
      >
        {{ result.display_name }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, inject } from 'vue';
import { useDraggable } from '../composables/useDraggable';
import { useSearch } from '../composables/useSearch';

// Props and emits
const props = defineProps<{
  visible: boolean;
}>();

const emit = defineEmits<{
  close: [];
  'update:visible': [value: boolean];
}>();

// Template refs
const panelRef = ref<HTMLElement>();
const searchInputRef = ref<HTMLInputElement>();

// Get map composable from injection
const getMapComposable = inject<() => any>('mapManager');

// Use composables
const { searchQuery, searchResults, isLoading, error } = useSearch();
const { isDragging, startDrag, startDragTouch } = useDraggable(panelRef);

// Focus input when panel becomes visible
watch(() => props.visible, async (newVisible) => {
  if (newVisible) {
    await nextTick();
    searchInputRef.value?.focus();
  }
});

const onInputFocus = () => {
  // Clear any previous error when user starts typing
  error.value = '';
};

const selectResult = (result: any) => {
  const mapComposable = getMapComposable?.();
  if (mapComposable) {
    mapComposable.setView([parseFloat(result.lat), parseFloat(result.lon)], 13);
    emit('close');
  }
};
</script>

<style scoped>
.search-panel {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 320px;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  max-height: 400px;
  display: flex;
  flex-direction: column;
}

.search-panel.dragging {
  user-select: none;
  cursor: move;
}

.search-header {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 4px 4px 0 0;
  gap: 8px;
}

.drag-handle {
  cursor: move;
  color: #6c757d;
  padding: 4px;
  border-radius: 2px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: color 0.2s ease;
}

.drag-handle:hover {
  color: #495057;
  background-color: rgba(0, 0, 0, 0.05);
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  outline: none;
  cursor: text;
}

.search-input:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.close-button {
  margin-left: 8px;
  width: 24px;
  height: 24px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  color: #6c757d;
}

.close-button:hover {
  background-color: #e9ecef;
  color: #495057;
}

.close-button:focus {
  outline: 2px solid #007bff;
  outline-offset: 1px;
}

.search-results {
  flex: 1;
  overflow-y: auto;
  max-height: 300px;
}

.search-result-item {
  padding: 12px;
  border-bottom: 1px solid #f1f3f4;
  cursor: pointer;
  font-size: 14px;
  line-height: 1.4;
  transition: background-color 0.2s ease;
}

.search-result-item:hover,
.search-result-item:focus {
  background-color: #f8f9fa;
  outline: none;
}

.search-result-item:last-child {
  border-bottom: none;
}

.loading,
.no-results,
.error {
  padding: 20px;
  text-align: center;
  font-size: 14px;
  color: #6c757d;
}

.error {
  color: #dc3545;
}

/* Responsive design */
@media (max-width: 768px) {
  .search-panel {
    width: 280px;
    top: 8px;
  }
}

@media (max-width: 480px) {
  .search-panel {
    width: calc(100vw - 20px);
    top: 5px;
  }
}
</style>
