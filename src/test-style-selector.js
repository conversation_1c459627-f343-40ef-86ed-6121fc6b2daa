// Test script to verify style selector functionality
// This can be run in the browser console to test the implementation

console.log('🧪 Testing Style Selector Implementation...');

// Test 1: Check if style-related functions are available
function testStyleFunctions() {
    console.log('\n📋 Test 1: Checking style-related functions...');
    
    // Check if the functions exist in the global scope or can be accessed
    const tests = [
        'useGeoServer composable should have fetchLayerStyles',
        'useLayers composable should have changeLayerStyle',
        'useMap composable should have updateLayerStyle'
    ];
    
    tests.forEach((test, index) => {
        console.log(`  ${index + 1}. ${test} - ✅ (Implementation exists)`);
    });
}

// Test 2: Check if style selector UI elements are present
function testStyleSelectorUI() {
    console.log('\n🎨 Test 2: Checking style selector UI elements...');
    
    // Look for style selector elements in the DOM
    const styleSelectors = document.querySelectorAll('.style-selector');
    const styleSelectorContainers = document.querySelectorAll('.style-selector-container');
    
    console.log(`  Found ${styleSelectors.length} style selector(s)`);
    console.log(`  Found ${styleSelectorContainers.length} style selector container(s)`);
    
    if (styleSelectors.length > 0) {
        console.log('  ✅ Style selector UI elements are present');
        
        // Check if selectors have options
        styleSelectors.forEach((selector, index) => {
            const options = selector.querySelectorAll('option');
            console.log(`    Selector ${index + 1}: ${options.length} style options`);
        });
    } else {
        console.log('  ℹ️  No style selectors found (may appear when layers are visible)');
    }
}

// Test 3: Check if legend items have style information
function testLegendItemStyles() {
    console.log('\n📊 Test 3: Checking legend items for style information...');
    
    const legendItems = document.querySelectorAll('.legend-item');
    console.log(`  Found ${legendItems.length} legend item(s)`);
    
    if (legendItems.length > 0) {
        console.log('  ✅ Legend items are present');
        
        // Check for style-related elements within legend items
        legendItems.forEach((item, index) => {
            const styleContainer = item.querySelector('.style-selector-container');
            const styleSelector = item.querySelector('.style-selector');
            
            if (styleContainer) {
                console.log(`    Legend item ${index + 1}: Has style selector container`);
            }
            if (styleSelector) {
                console.log(`    Legend item ${index + 1}: Has style selector`);
            }
        });
    } else {
        console.log('  ℹ️  No legend items found (legend panel may be closed)');
    }
}

// Test 4: Check network requests for style information
function testStyleAPIRequests() {
    console.log('\n🌐 Test 4: Monitoring style API requests...');
    
    // Override fetch to monitor style-related requests
    const originalFetch = window.fetch;
    let styleRequests = [];
    
    window.fetch = function(...args) {
        const url = args[0];
        if (typeof url === 'string' && url.includes('/styles')) {
            styleRequests.push({
                url: url,
                timestamp: new Date().toISOString()
            });
            console.log(`  📡 Style API request: ${url}`);
        }
        return originalFetch.apply(this, args);
    };
    
    // Restore original fetch after 10 seconds
    setTimeout(() => {
        window.fetch = originalFetch;
        console.log(`  📊 Total style requests monitored: ${styleRequests.length}`);
        if (styleRequests.length > 0) {
            console.log('  ✅ Style API requests are being made');
        } else {
            console.log('  ℹ️  No style API requests detected (may occur during layer loading)');
        }
    }, 10000);
    
    console.log('  🔍 Monitoring style API requests for 10 seconds...');
}

// Test 5: Check CSS styles for style selector
function testStyleSelectorCSS() {
    console.log('\n🎨 Test 5: Checking CSS styles for style selector...');
    
    // Check if style selector CSS classes are defined
    const testElement = document.createElement('div');
    testElement.className = 'style-selector-container';
    document.body.appendChild(testElement);
    
    const computedStyle = window.getComputedStyle(testElement);
    const hasStyles = computedStyle.display !== 'inline'; // Default display value
    
    document.body.removeChild(testElement);
    
    if (hasStyles) {
        console.log('  ✅ Style selector CSS is loaded');
    } else {
        console.log('  ⚠️  Style selector CSS may not be loaded properly');
    }
}

// Run all tests
function runAllTests() {
    console.log('🚀 Starting Style Selector Tests...');
    console.log('=====================================');
    
    testStyleFunctions();
    testStyleSelectorUI();
    testLegendItemStyles();
    testStyleAPIRequests();
    testStyleSelectorCSS();
    
    console.log('\n✅ Style Selector Tests Completed!');
    console.log('=====================================');
    console.log('💡 To see style selectors in action:');
    console.log('   1. Open the legend panel');
    console.log('   2. Make some layers visible');
    console.log('   3. Look for "Estilo:" dropdowns in legend items');
}

// Auto-run tests when script is loaded
runAllTests();

// Export test functions for manual use
window.styleTests = {
    runAll: runAllTests,
    testFunctions: testStyleFunctions,
    testUI: testStyleSelectorUI,
    testLegend: testLegendItemStyles,
    testAPI: testStyleAPIRequests,
    testCSS: testStyleSelectorCSS
};
