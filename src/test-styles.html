<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test GeoServer Styles API</title>
</head>
<body>
    <h1>Test GeoServer Styles API</h1>
    <div id="results"></div>

    <script>
        // Test function to check if GeoServer REST API is accessible
        async function testStylesAPI() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // Test the REST API endpoint
                const testUrl = '/geoserver/rest/layers.json';
                console.log('Testing URL:', testUrl);
                
                const response = await fetch(testUrl);
                console.log('Response status:', response.status);
                
                if (response.ok) {
                    const data = await response.json();
                    console.log('Layers data:', data);
                    
                    resultsDiv.innerHTML = `
                        <h2>✅ GeoServer REST API is accessible</h2>
                        <p>Status: ${response.status}</p>
                        <pre>${JSON.stringify(data, null, 2)}</pre>
                    `;
                    
                    // Test getting styles for a specific layer if layers exist
                    if (data.layers && data.layers.layer && data.layers.layer.length > 0) {
                        const firstLayer = data.layers.layer[0];
                        const layerName = firstLayer.name;
                        
                        console.log('Testing styles for layer:', layerName);
                        const stylesUrl = `/geoserver/rest/layers/${layerName}/styles.json`;
                        
                        try {
                            const stylesResponse = await fetch(stylesUrl);
                            if (stylesResponse.ok) {
                                const stylesData = await stylesResponse.json();
                                console.log('Styles data:', stylesData);
                                
                                resultsDiv.innerHTML += `
                                    <h3>✅ Styles for layer "${layerName}":</h3>
                                    <pre>${JSON.stringify(stylesData, null, 2)}</pre>
                                `;
                            } else {
                                resultsDiv.innerHTML += `
                                    <h3>❌ Failed to get styles for layer "${layerName}"</h3>
                                    <p>Status: ${stylesResponse.status}</p>
                                `;
                            }
                        } catch (err) {
                            resultsDiv.innerHTML += `
                                <h3>❌ Error getting styles for layer "${layerName}"</h3>
                                <p>Error: ${err.message}</p>
                            `;
                        }
                    }
                } else {
                    resultsDiv.innerHTML = `
                        <h2>❌ GeoServer REST API is not accessible</h2>
                        <p>Status: ${response.status}</p>
                        <p>Response: ${await response.text()}</p>
                    `;
                }
            } catch (error) {
                console.error('Error:', error);
                resultsDiv.innerHTML = `
                    <h2>❌ Error testing GeoServer REST API</h2>
                    <p>Error: ${error.message}</p>
                    <p>This might be due to CORS policy or GeoServer not being accessible.</p>
                `;
            }
        }

        // Run the test when page loads
        testStylesAPI();
    </script>
</body>
</html>
