import { ref, computed, readonly } from 'vue';
import { config, getGeoServerCapabilitiesUrl } from '../config';
import { GeoLayer, LayerType } from '../types';

// Global reactive state for GeoServer data
const isLoading = ref(false);
const error = ref<string>('');
const layers = ref<GeoLayer[]>([]);
const lastFetchTime = ref<number>(0);

// Cache duration in milliseconds (5 minutes)
const CACHE_DURATION = 5 * 60 * 1000;

/**
 * Vue composable for managing GeoServer layer data with reactive state.
 * 
 * Features:
 * - Reactive loading and error states
 * - Cached layer data to avoid unnecessary re-fetching
 * - Shared state between components
 * - Proper error handling
 * - TypeScript support
 */
export function useGeoServer() {
  /**
   * Fetch layers from GeoServer with caching and error handling
   */
  const fetchLayers = async (forceRefresh = false): Promise<GeoLayer[]> => {
    // Check if we have cached data and it's still valid
    const now = Date.now();
    const isCacheValid = (now - lastFetchTime.value) < CACHE_DURATION;
    
    if (!forceRefresh && layers.value.length > 0 && isCacheValid) {
      console.log('Using cached GeoServer layers');
      return [...layers.value] as GeoLayer[];
    }

    isLoading.value = true;
    error.value = '';

    try {
      const response = await fetch(getGeoServerCapabilitiesUrl());

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const text = await response.text();

      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(text, 'text/xml');

      // Check for XML parsing errors
      const parseError = xmlDoc.querySelector('parsererror');
      if (parseError) {
        console.error('XML parsing error:', parseError.textContent);
        throw new Error('Failed to parse XML response from GeoServer');
      }
      const layerElements = xmlDoc.querySelectorAll('Layer > Name');
      
      const fetchedLayers: GeoLayer[] = [];

      for (let index = 0; index < layerElements.length; index++) {
        const element = layerElements[index];
        const layerName = element.textContent;

        const titleElement = element.closest('Layer')?.querySelector('Title');
        const fullName = titleElement ? titleElement.textContent : layerName;
        const originalName = fullName || '';

        let name = fullName || '';
        let groupPath = '';

        if (fullName && fullName.includes('/')) {
          const parts = fullName.split('/');
          name = parts.pop() || '';
          groupPath = parts.join('/');
        }

        let abstract = '';
        const layerParent = element.closest('Layer');
        if (layerParent) {
          const abstractElement = layerParent.querySelector('Abstract');
          if (abstractElement && abstractElement.textContent && abstractElement.textContent.trim() !== '') {
            abstract = abstractElement.textContent.trim();
          }
        }

        const layer: GeoLayer = {
          id: layerName.replace(':', '_'),
          name: name,
          originalName: originalName,
          groupPath: groupPath,
          type: LayerType.WMS,
          url: config.server.geoserverWmsPath,
          layer: layerName,
          visible: false,
          legend_url: `${config.server.geoserverWmsPath}?REQUEST=GetLegendGraphic&VERSION=1.0.0&FORMAT=image/png&WIDTH=20&HEIGHT=20&LAYER=${layerName}`,
          abstract: abstract,
          opacity: config.layer.defaultOpacity,
          zIndex: config.layer.defaultZIndex + index,
          layer_instance: undefined, // Will be set by map composable when layer is added
        };

        fetchedLayers.push(layer);
        console.log(`Added layer: ${layer.name} (${layer.id})`);
      }

      // Update reactive state
      layers.value = fetchedLayers as GeoLayer[];
      lastFetchTime.value = now;
      
      console.log(`Total layers found: ${fetchedLayers.length}`);
      return fetchedLayers;

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      console.error('Error fetching layers from GeoServer:', err);
      error.value = `Failed to fetch layers from GeoServer: ${errorMessage}`;
      return [];
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * Clear cached data and force a fresh fetch on next request
   */
  const clearCache = () => {
    layers.value = [];
    lastFetchTime.value = 0;
    error.value = '';
  };

  /**
   * Check if cached data is available and valid
   */
  const hasCachedData = computed(() => {
    const now = Date.now();
    const isCacheValid = (now - lastFetchTime.value) < CACHE_DURATION;
    return layers.value.length > 0 && isCacheValid;
  });

  /**
   * Get cache age in minutes
   */
  const cacheAge = computed(() => {
    if (lastFetchTime.value === 0) return 0;
    return Math.floor((Date.now() - lastFetchTime.value) / (1000 * 60));
  });

  return {
    // Reactive state (readonly to prevent external mutation)
    isLoading: readonly(isLoading),
    error: readonly(error),
    layers: readonly(layers),
    
    // Computed properties
    hasCachedData,
    cacheAge,
    
    // Methods
    fetchLayers,
    clearCache,
  };
}
