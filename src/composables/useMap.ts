import { ref, computed, reactive, onUnmounted, nextTick } from 'vue';
import L from 'leaflet';
import { GeoLayer, LayerType } from '../types';
import { config } from '../config';

/**
 * Vue composable for managing the Leaflet map with reactive state.
 * This replaces the MapManager class with a Vue 3 composable approach.
 * 
 * Features:
 * - Reactive map state (center, zoom, bounds)
 * - Base layer management
 * - Layer operations (add, remove, toggle visibility)
 * - Map controls and interactions
 * - Automatic cleanup on unmount
 */
export function useMap() {
  // Reactive state
  const mapInstance = ref<L.Map | null>(null);
  const isInitialized = ref(false);
  const isLoading = ref(false);
  const error = ref('');
  
  // Map state
  const center = ref<[number, number]>([
    config.map.defaultCenter.lat, 
    config.map.defaultCenter.lng
  ]);
  const zoom = ref<number>(config.map.defaultZoom);
  const bounds = ref<L.LatLngBounds | null>(null);
  
  // Base layers state
  const currentBaseLayer = ref<string>('osm');
  const baseLayers = reactive<Record<string, L.TileLayer>>({});
  
  // Layers state
  const layers = ref<GeoLayer[]>([]);
  
  // Computed properties
  const mapCenter = computed(() => center.value);
  const mapZoom = computed(() => zoom.value);
  const mapBounds = computed(() => bounds.value);
  
  /**
   * Initialize the map with the given element ID
   */
  const initializeMap = async (mapElementId: string): Promise<void> => {
    if (isInitialized.value) {
      console.warn('Map is already initialized');
      return;
    }
    
    isLoading.value = true;
    error.value = '';
    
    try {
      const ecuadorBounds = L.latLngBounds(
        [config.map.ecuadorBounds.southwest.lat, config.map.ecuadorBounds.southwest.lng],
        [config.map.ecuadorBounds.northeast.lat, config.map.ecuadorBounds.northeast.lng]
      );

      mapInstance.value = L.map(mapElementId, {
        center: center.value,
        zoom: zoom.value,
        zoomControl: false,
        maxBounds: ecuadorBounds,
        maxBoundsViscosity: config.map.maxBoundsViscosity,
        minZoom: config.map.minZoom,
      });
      
      // Set initial bounds
      bounds.value = ecuadorBounds;
      
      // Add zoom control
      L.control.zoom({
        position: 'bottomleft'
      }).addTo(mapInstance.value as any);

      // Initialize base layers
      initializeBaseLayers();

      // Add scale control
      L.control.scale().addTo(mapInstance.value as any);
      
      // Set up event listeners for reactive state updates
      setupMapEventListeners();
      
      // Zoom to home initially
      zoomToHome();
      
      isInitialized.value = true;
      console.log('Map initialized successfully');
      
    } catch (err) {
      error.value = `Failed to initialize map: ${err}`;
      console.error('Map initialization error:', err);
    } finally {
      isLoading.value = false;
    }
  };
  
  /**
   * Initialize base layers
   */
  const initializeBaseLayers = (): void => {
    if (!mapInstance.value) return;
    
    baseLayers.osm = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
      maxZoom: 19,
    }).addTo(mapInstance.value as any);

    baseLayers.satellite = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
      attribution: 'Tiles &copy; Esri &mdash; Source: Esri, i-cubed, USDA, USGS, AEX, GeoEye, Getmapping, Aerogrid, IGN, IGP, UPR-EGP, and the GIS User Community',
      maxZoom: 19,
    });

    baseLayers.topo = L.tileLayer('https://{s}.tile.opentopomap.org/{z}/{x}/{y}.png', {
      attribution: 'Map data: &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors, <a href="http://viewfinderpanoramas.org">SRTM</a> | Map style: &copy; <a href="https://opentopomap.org">OpenTopoMap</a> (<a href="https://creativecommons.org/licenses/by-sa/3.0/">CC-BY-SA</a>)',
      maxZoom: 17,
    });

    L.control.layers(
      {
        'OpenStreetMap': baseLayers.osm,
        'Satellite': baseLayers.satellite,
        'Topographic': baseLayers.topo,
      },
      {},
      { position: 'topright' }
    ).addTo(mapInstance.value as any);
  };
  
  /**
   * Set up event listeners for reactive state updates
   */
  const setupMapEventListeners = (): void => {
    if (!mapInstance.value) return;
    
    mapInstance.value.on('moveend', () => {
      if (mapInstance.value) {
        const newCenter = mapInstance.value.getCenter();
        center.value = [newCenter.lat, newCenter.lng];
        bounds.value = mapInstance.value.getBounds();
      }
    });
    
    mapInstance.value.on('zoomend', () => {
      if (mapInstance.value) {
        zoom.value = mapInstance.value.getZoom() as number;
      }
    });
    
    mapInstance.value.on('baselayerchange', (e: any) => {
      // Update current base layer when user changes it via layer control
      console.log('Base layer changed via layer control:', e.layer);
      const layerName = Object.keys(baseLayers).find(key => baseLayers[key] === e.layer);
      if (layerName) {
        currentBaseLayer.value = layerName;

        // Ensure proper layer ordering after base layer change
        ensureProperLayerOrdering();

        console.log(`Base layer ordering fixed for '${layerName}'`);
      }
    });
  };
  
  /**
   * Get the map instance (for backward compatibility)
   */
  const getMap = (): any => {
    return mapInstance.value;
  };
  
  /**
   * Add a layer to the map
   */
  const addLayer = (layer: GeoLayer): void => {
    if (!mapInstance.value) return;
    
    const existingLayerIndex = layers.value.findIndex(l => l.id === layer.id);
    if (existingLayerIndex !== -1) {
      removeLayer(layer.id);
    }
    
    layers.value.push(layer);
    
    if (layer.visible) {
      createAndAddLayer(layer);
    }
  };
  
  /**
   * Remove a layer from the map
   */
  const removeLayer = (layerId: string): void => {
    if (!mapInstance.value) return;
    
    const layerIndex = layers.value.findIndex(l => l.id === layerId);
    if (layerIndex !== -1) {
      const layer = layers.value[layerIndex];
      if (layer.layer_instance) {
        mapInstance.value.removeLayer(layer.layer_instance as any);
      }
      layers.value.splice(layerIndex, 1);
    }
  };
  
  /**
   * Toggle layer visibility
   */
  const toggleLayerVisibility = (layerId: string, visible?: boolean): void => {
    if (!mapInstance.value) return;
    
    const layer = layers.value.find(l => l.id === layerId);
    if (!layer) return;

    layer.visible = visible !== undefined ? visible : !layer.visible;

    if (layer.visible) {
      if (!layer.layer_instance) {
        createAndAddLayer(layer as any);
      } else {
        mapInstance.value.addLayer(layer.layer_instance as any);
        bringLayerToTop(layerId);
      }
    } else {
      if (layer.layer_instance) {
        mapInstance.value.removeLayer(layer.layer_instance as any);
      }
    }
  };
  
  /**
   * Bring layer to top
   */
  const bringLayerToTop = (layerId: string): void => {
    const layer = layers.value.find(l => l.id === layerId);
    if (!layer) return;

    if (layer.layer_instance && 'bringToFront' in layer.layer_instance) {
      (layer.layer_instance as any).bringToFront();
    }
  };

  /**
   * Ensure proper layer ordering with base layers at bottom and overlay layers on top
   */
  const ensureProperLayerOrdering = (): void => {
    if (!mapInstance.value) return;

    // Ensure current base layer is at the bottom
    const currentBase = baseLayers[currentBaseLayer.value];
    if (currentBase && 'bringToBack' in currentBase) {
      (currentBase as any).bringToBack();
    }
  };
  
  /**
   * Update layer opacity
   */
  const updateLayerOpacity = (layerId: string, opacity: number): void => {
    const layer = layers.value.find(l => l.id === layerId);
    if (!layer || !layer.layer_instance) return;

    layer.opacity = opacity;

    if (layer.layer_instance) {
      if ('setOpacity' in layer.layer_instance) {
        (layer.layer_instance as any).setOpacity(opacity);
      } else if ('setStyle' in layer.layer_instance) {
        (layer.layer_instance as any).setStyle({ opacity });
      }
    }
  };

  /**
   * Update layer style (for WMS layers)
   */
  const updateLayerStyle = (layerId: string, styleName: string): void => {
    const layer = layers.value.find(l => l.id === layerId);
    if (!layer || !mapInstance.value) return;

    // For WMS layers, we need to recreate the layer with the new style
    if (layer.type === LayerType.WMS && layer.layer_instance) {
      // Remove the current layer instance
      mapInstance.value.removeLayer(layer.layer_instance as any);

      // Create new WMS layer with the specified style
      const wmsLayer = L.tileLayer.wms(layer.url!, {
        layers: layer.layer!,
        styles: styleName, // Add the style parameter
        format: 'image/png',
        transparent: true,
        opacity: layer.opacity || config.layer.defaultOpacity,
        zIndex: layer.zIndex || config.layer.defaultZIndex,
      });

      // Update the layer instance
      layer.layer_instance = wmsLayer as any;

      // Add the new layer to the map
      wmsLayer.addTo(mapInstance.value as any);
      bringLayerToTop(layerId);

      console.log(`Updated style for layer ${layer.name} to ${styleName}`);
    }
  };
  
  /**
   * Zoom to home bounds
   */
  const zoomToHome = (): void => {
    if (!mapInstance.value) return;
    
    const homeBounds = L.latLngBounds(
      [config.map.homeBounds.southwest.lat, config.map.homeBounds.southwest.lng],
      [config.map.homeBounds.northeast.lat, config.map.homeBounds.northeast.lng]
    );
    mapInstance.value.fitBounds(homeBounds);
  };
  
  /**
   * Create and add layer to map
   */
  const createAndAddLayer = (layer: GeoLayer): void => {
    if (!mapInstance.value) return;

    if (layer.type === LayerType.WMS && layer.url && layer.layer) {
      const wmsLayer = L.tileLayer.wms(layer.url, {
        layers: layer.layer,
        format: 'image/png',
        transparent: true,
        opacity: layer.opacity || config.layer.defaultOpacity,
        zIndex: layer.zIndex || config.layer.defaultZIndex,
      });
      layer.layer_instance = wmsLayer as any;
      wmsLayer.addTo(mapInstance.value as any);
      bringLayerToTop(layer.id);
    } else if (layer.type === LayerType.GEOJSON && layer.url) {
      createGeoJSONLayer(layer);
    }
  };

  /**
   * Create GeoJSON layer
   */
  const createGeoJSONLayer = async (layer: GeoLayer): Promise<void> => {
    if (!layer.url || !mapInstance.value) return;

    try {
      const response = await fetch(layer.url);
      const geoJsonData = await response.json();

      const geoJsonLayer = L.geoJSON(geoJsonData, {
        style: {
          color: '#3388ff',
          weight: 2,
          opacity: 0.8,
          fillOpacity: 0.6
        }
      });

      if (layer.opacity !== undefined) {
        geoJsonLayer.setStyle({ opacity: layer.opacity, fillOpacity: layer.opacity * 0.7 });
      }

      layer.layer_instance = geoJsonLayer as any;
      geoJsonLayer.addTo(mapInstance.value as any);
      bringLayerToTop(layer.id);
    } catch (err) {
      console.error(`Error loading GeoJSON layer ${layer.id}:`, err);
    }
  };

  /**
   * Change base layer
   */
  const changeBaseLayer = (layerName: string): void => {
    if (!mapInstance.value || !baseLayers[layerName]) {
      console.warn(`Base layer '${layerName}' not found or map not initialized`);
      return;
    }

    console.log(`Changing base layer from '${currentBaseLayer.value}' to '${layerName}'`);

    // Remove current base layer
    if (baseLayers[currentBaseLayer.value]) {
      mapInstance.value.removeLayer(baseLayers[currentBaseLayer.value]);
    }

    // Add new base layer
    baseLayers[layerName].addTo(mapInstance.value as any);
    currentBaseLayer.value = layerName;

    // Ensure proper layer ordering after base layer change
    ensureProperLayerOrdering();

    console.log(`Base layer changed to '${layerName}' successfully`);
  };

  /**
   * Set map view
   */
  const setView = (newCenter: [number, number], newZoom: number): void => {
    if (!mapInstance.value) return;

    mapInstance.value.setView(newCenter, newZoom);
    center.value = newCenter;
    zoom.value = newZoom as number;
  };

  /**
   * Fit bounds
   */
  const fitBounds = (newBounds: L.LatLngBounds): void => {
    if (!mapInstance.value) return;

    mapInstance.value.fitBounds(newBounds);
    bounds.value = newBounds;
  };

  /**
   * Cleanup function
   */
  const cleanup = (): void => {
    if (mapInstance.value) {
      mapInstance.value.remove();
      mapInstance.value = null;
    }

    // Clear reactive state
    layers.value = [];
    Object.keys(baseLayers).forEach(key => delete baseLayers[key]);
    isInitialized.value = false;
    isLoading.value = false;
    error.value = '';
  };

  // Auto cleanup on unmount
  onUnmounted(() => {
    cleanup();
  });

  return {
    // Reactive state
    mapInstance: computed(() => mapInstance.value),
    isInitialized: computed(() => isInitialized.value),
    isLoading: computed(() => isLoading.value),
    error: computed(() => error.value),

    // Map state
    center: mapCenter,
    zoom: mapZoom,
    bounds: mapBounds,
    currentBaseLayer: computed(() => currentBaseLayer.value),

    // Layers
    layers: computed(() => layers.value),

    // Core methods
    initializeMap,
    cleanup,

    // Map operations
    getMap,
    setView,
    fitBounds,
    zoomToHome,

    // Base layer operations
    changeBaseLayer,

    // Layer operations
    addLayer,
    removeLayer,
    toggleLayerVisibility,
    updateLayerOpacity,
    updateLayerStyle,
    bringLayerToTop,
    ensureProperLayerOrdering,

    // Utility methods
    getLayers: () => layers.value,
    getLayerById: (id: string) => layers.value.find(l => l.id === id),
  };
}
