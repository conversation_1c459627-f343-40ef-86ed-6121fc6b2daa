# Vue Composables

This directory contains Vue 3 composables that provide reactive state management for the geoportal application.

## useGeoServer Composable

The `useGeoServer` composable manages GeoServer layer data with reactive state and caching.

### Features

- **Reactive State**: Loading, error, and layer data are reactive and automatically trigger UI updates
- **Caching**: Intelligent caching system to avoid unnecessary network requests
- **Shared State**: State is shared between all components that use the composable
- **Error Handling**: Comprehensive error handling with user-friendly error messages
- **TypeScript Support**: Full TypeScript support with proper type definitions

### Usage

```typescript
import { useGeoServer } from '@/composables/useGeoServer';

// In a Vue component
export default defineComponent({
  setup() {
    const geoServer = useGeoServer();

    // Fetch layers
    onMounted(async () => {
      await geoServer.fetchLayers();
    });

    return {
      isLoading: geoServer.isLoading,
      error: geoServer.error,
      layers: geoServer.layers,
      hasCachedData: geoServer.hasCachedData,
      cacheAge: geoServer.cacheAge,
      clearCache: geoServer.clearCache
    };
  }
});
```

## useLayers Composable

The `useLayers` composable is the modern, Vue 3-based replacement for the deprecated `LayerManager` class. It provides reactive state management for GeoServer layers with full Vue integration and uses the `useGeoServer` composable internally.

## Features

- **Reactive State**: All layer and group data is reactive and automatically triggers UI updates
- **Automatic Legend Management**: Computed properties for visible layers used in legends
- **Layer Ordering**: Smart layer ordering without unnecessary z-index increments
- **Parent-Child Relationships**: Proper visibility management between groups and layers
- **Loading States**: Built-in loading and error state management
- **Type Safety**: Full TypeScript support with proper type definitions

## Usage

### Basic Setup

```typescript
import { useLayers } from '@/composables/useLayers';

// In a Vue component
export default defineComponent({
  setup() {
    const mapManager = inject('mapManager');
    const layerManager = useLayers(mapManager);
    
    // Initialize layers
    onMounted(async () => {
      await layerManager.initialize();
    });
    
    return {
      layers: layerManager.layers,
      rootGroups: layerManager.rootGroups,
      visibleLayers: layerManager.visibleLayers,
      isLoading: layerManager.isLoading,
      error: layerManager.error,
      toggleLayerVisibility: layerManager.toggleLayerVisibility
    };
  }
});
```

### Reactive State Properties

- `layers`: All available layers
- `groups`: All layer groups
- `rootGroups`: Top-level groups (no parent)
- `ungroupedLayers`: Layers not in any group
- `visibleLayers`: Currently visible layers (for legends)
- `layersByZIndex`: Layers sorted by z-index
- `isLoading`: Loading state
- `error`: Error message if any

### Methods

#### Core Methods
- `initialize()`: Initialize and fetch layers from GeoServer
- `cleanup()`: Clean up resources
- `fetchLayers()`: Manually fetch layers

#### Group Methods
- `toggleGroupCollapse(groupId)`: Toggle group expand/collapse
- `toggleGroupVisibility(groupId)`: Toggle group visibility

#### Layer Methods
- `toggleLayerVisibility(layerId, visible?)`: Toggle layer visibility
- `updateLayerOpacity(layerId, opacity)`: Update layer opacity
- `bringLayerToTop(layerId)`: Bring layer to top of stack
- `reorderLayers(layerIds)`: Reorder layers by ID array

#### Utility Methods
- `getLayerById(layerId)`: Get layer by ID
- `getGroupById(groupId)`: Get group by ID
- `clearLayers()`: Remove all layers
- `getLayers()`: Get layers array
- `getVisibleLayers()`: Get visible layers array

## Migration from LayerManager

The old `LayerManager` class has been deprecated. Here's how to migrate:

### Before (LayerManager)
```javascript
// Old approach with manual DOM manipulation
const layerManager = new LayerManager(mapManager, geoServerService, uiManager);
layerManager.toggleLayerVisibility(layerId);
// Manual UI updates required
```

### After (useLayers)
```typescript
// New approach with reactive state
const layerManager = useLayers(mapManager);
layerManager.toggleLayerVisibility(layerId);
// UI automatically updates via reactivity
```

## Key Improvements

1. **Reactivity**: No more manual UI updates - Vue handles everything
2. **Performance**: Better performance through Vue's reactivity system
3. **Type Safety**: Full TypeScript support
4. **Composability**: Can be used in any Vue component
5. **Testing**: Easier to test with reactive state
6. **Memory Management**: Automatic cleanup on component unmount

## Layer Ordering Logic

The composable implements smart layer ordering that avoids unnecessary z-index increments:

- When a layer is made visible, it's brought to the top using `bringLayerToTop()`
- The `reorderLayers()` method allows efficient reordering
- Z-index values are managed efficiently without constant incrementation

## Error Handling

The composable provides comprehensive error handling:

- Network errors during layer fetching
- Invalid layer configurations
- MapManager integration issues
- Reactive error state for UI display

## Best Practices

1. **Always initialize**: Call `initialize()` after mapManager is ready
2. **Use computed properties**: Leverage reactive computed properties for derived state
3. **Handle loading states**: Use `isLoading` and `error` for user feedback
4. **Cleanup**: The composable automatically cleans up on unmount
5. **Type safety**: Use TypeScript for better development experience

## Example Component

```vue
<template>
  <div>
    <div v-if="isLoading">Loading layers...</div>
    <div v-else-if="error">{{ error }}</div>
    <div v-else>
      <div v-for="group in rootGroups" :key="group.id">
        <button @click="toggleGroupVisibility(group.id)">
          {{ group.name }} ({{ group.visible ? 'Visible' : 'Hidden' }})
        </button>
      </div>
      <div v-for="layer in ungroupedLayers" :key="layer.id">
        <button @click="toggleLayerVisibility(layer.id)">
          {{ layer.name }} ({{ layer.visible ? 'Visible' : 'Hidden' }})
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { inject, onMounted } from 'vue';
import { useLayers } from '@/composables/useLayers';

const mapManager = inject('mapManager');
const {
  rootGroups,
  ungroupedLayers,
  isLoading,
  error,
  initialize,
  toggleGroupVisibility,
  toggleLayerVisibility
} = useLayers(mapManager);

onMounted(async () => {
  await initialize();
});
</script>
```
