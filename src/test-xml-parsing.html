<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test XML Style Parsing</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        pre { background-color: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .style-item { margin: 5px 0; padding: 5px; background-color: #e9ecef; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Test XML Style Parsing</h1>
    <p>This page demonstrates how styles are extracted from WMS GetCapabilities XML responses.</p>

    <div id="results"></div>

    <script>
        // Sample XML that represents a typical GeoServer GetCapabilities response structure
        const sampleXML = `
        <Layer>
            <Name>workspace:layer_name</Name>
            <Title>Sample Layer</Title>
            <Abstract>This is a sample layer with multiple styles</Abstract>
            <Style>
                <Name>default</Name>
                <Title>Default Style</Title>
                <Abstract>The default styling for this layer</Abstract>
            </Style>
            <Style>
                <Name>red_style</Name>
                <Title>Red Styling</Title>
                <Abstract>A red color scheme for the layer</Abstract>
            </Style>
            <Style>
                <Name>blue_style</Name>
                <Title>Blue Styling</Title>
                <Abstract>A blue color scheme for the layer</Abstract>
            </Style>
        </Layer>
        `;

        // Function that mimics the extractStylesFromLayer function
        function extractStylesFromLayer(layerElement, layerName) {
            const styleInfo = {
                styles: [],
                currentStyle: 'default',
                defaultStyle: 'default',
                loading: false,
                error: null
            };

            if (!layerElement) {
                return styleInfo;
            }

            try {
                // Find all Style elements within this Layer
                const styleElements = layerElement.querySelectorAll('Style');
                const styles = [];

                styleElements.forEach((styleElement, index) => {
                    const nameElement = styleElement.querySelector('Name');
                    const titleElement = styleElement.querySelector('Title');
                    const abstractElement = styleElement.querySelector('Abstract');

                    if (nameElement && nameElement.textContent) {
                        const styleName = nameElement.textContent.trim();
                        const style = {
                            name: styleName,
                            title: titleElement?.textContent?.trim() || styleName,
                            abstract: abstractElement?.textContent?.trim(),
                            isDefault: index === 0 // First style is typically the default
                        };
                        styles.push(style);
                    }
                });

                if (styles.length > 0) {
                    styleInfo.styles = styles;
                    styleInfo.currentStyle = styles[0].name;
                    styleInfo.defaultStyle = styles[0].name;
                }

                console.log(`Found ${styles.length} styles for layer ${layerName}:`, styles.map(s => s.name));
            } catch (err) {
                console.warn(`Error extracting styles for layer ${layerName}:`, err);
                styleInfo.error = 'Failed to extract styles from capabilities';
            }

            return styleInfo;
        }

        // Function to construct legend URL with style
        function constructLegendUrl(layerName, styleName) {
            const baseUrl = `/geoserver/wms?REQUEST=GetLegendGraphic&VERSION=1.0.0&FORMAT=image/png&WIDTH=20&HEIGHT=20&LAYER=${layerName}`;
            return `${baseUrl}&STYLE=${styleName}`;
        }

        // Test the XML parsing
        function testXMLParsing() {
            const resultsDiv = document.getElementById('results');
            
            try {
                // Parse the sample XML
                const parser = new DOMParser();
                const xmlDoc = parser.parseFromString(sampleXML, 'text/xml');
                
                // Check for parsing errors
                const parseError = xmlDoc.querySelector('parsererror');
                if (parseError) {
                    throw new Error('XML parsing failed');
                }

                const layerElement = xmlDoc.querySelector('Layer');
                const layerName = layerElement.querySelector('Name').textContent;
                
                // Extract styles using our function
                const styleInfo = extractStylesFromLayer(layerElement, layerName);
                
                // Display results
                let html = `
                    <div class="test-section success">
                        <h2>✅ XML Parsing Successful</h2>
                        <p><strong>Layer Name:</strong> ${layerName}</p>
                        <p><strong>Styles Found:</strong> ${styleInfo.styles.length}</p>
                        <p><strong>Default Style:</strong> ${styleInfo.defaultStyle}</p>
                    </div>
                `;

                if (styleInfo.styles.length > 0) {
                    html += `
                        <div class="test-section">
                            <h3>📋 Extracted Styles</h3>
                    `;
                    
                    styleInfo.styles.forEach((style, index) => {
                        const legendUrl = constructLegendUrl(layerName, style.name);
                        html += `
                            <div class="style-item">
                                <strong>${style.name}</strong> ${style.isDefault ? '(Default)' : ''}
                                <br><em>Title:</em> ${style.title}
                                <br><em>Abstract:</em> ${style.abstract || 'No description'}
                                <br><em>Legend URL:</em> <code>${legendUrl}</code>
                            </div>
                        `;
                    });
                    
                    html += `</div>`;
                }

                html += `
                    <div class="test-section">
                        <h3>🔍 Sample XML Structure</h3>
                        <pre>${sampleXML.trim()}</pre>
                    </div>
                `;

                html += `
                    <div class="test-section">
                        <h3>📊 Parsed Style Information</h3>
                        <pre>${JSON.stringify(styleInfo, null, 2)}</pre>
                    </div>
                `;

                resultsDiv.innerHTML = html;

            } catch (error) {
                resultsDiv.innerHTML = `
                    <div class="test-section error">
                        <h2>❌ XML Parsing Failed</h2>
                        <p>Error: ${error.message}</p>
                    </div>
                `;
            }
        }

        // Run the test when page loads
        testXMLParsing();
    </script>
</body>
</html>
