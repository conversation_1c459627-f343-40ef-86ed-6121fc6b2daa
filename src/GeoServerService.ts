/**
 * @deprecated This GeoServerService class is deprecated.
 * Use the useGeoServer composable from src/composables/useGeoServer.ts instead.
 *
 * The useGeoServer composable provides:
 * - Reactive loading and error states
 * - Cached layer data to avoid unnecessary re-fetching
 * - Shared state between components using Vue's reactivity system
 * - Better integration with Vue 3 composition API
 * - Proper TypeScript support
 *
 * This file will be removed in a future version.
 */

import { config, getGeoServerCapabilitiesUrl } from './config';
import { GeoLayer, LayerType } from './types';

/**
 * @deprecated Use useGeoServer composable instead
 */
export class GeoServerService {
    public async fetchLayers(): Promise<GeoLayer[]> {
        try {
            console.log('Fetching from URL:', getGeoServerCapabilitiesUrl());
            const response = await fetch(getGeoServerCapabilitiesUrl());
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const text = await response.text();
            console.log('Response text length:', text.length);
            console.log('Response text preview:', text.substring(0, 500));

            const parser = new DOMParser();
            const xmlDoc = parser.parseFromString(text, 'text/xml');

            // Check for XML parsing errors
            const parseError = xmlDoc.querySelector('parsererror');
            if (parseError) {
                console.error('XML parsing error:', parseError.textContent);
                throw new Error('Failed to parse XML response');
            }

            const layerElements = xmlDoc.querySelectorAll('Layer > Name');
            console.log('Found layer elements:', layerElements.length);
            const layers: GeoLayer[] = [];

            for (let index = 0; index < layerElements.length; index++) {
                const element = layerElements[index];
                const layerName = element.textContent;
                console.log(`Processing layer ${index + 1}/${layerElements.length}: ${layerName}`);

                if (layerName && layerName.startsWith(`${config.server.geoserverWorkspace}:`)) {
                    console.log(`Layer ${layerName} matches workspace ${config.server.geoserverWorkspace}`);
                    const titleElement = element.closest('Layer')?.querySelector('Title');
                    const fullName = titleElement ? titleElement.textContent : layerName.split(':')[1];
                    const originalName = fullName || '';

                    let name = fullName || '';
                    let groupPath = '';

                    if (fullName && fullName.includes('/')) {
                        const parts = fullName.split('/');
                        name = parts.pop() || '';
                        groupPath = parts.join('/');
                    }

                    let abstract = '';
                    const layerParent = element.closest('Layer');
                    if (layerParent) {
                        const abstractElement = layerParent.querySelector('Abstract');
                        if (abstractElement && abstractElement.textContent && abstractElement.textContent.trim() !== '') {
                            abstract = abstractElement.textContent.trim();
                        }
                    }

                    const layer: GeoLayer = {
                        id: layerName.replace(':', '_'),
                        name: name,
                        originalName: originalName,
                        groupPath: groupPath,
                        type: LayerType.WMS,
                        url: config.server.geoserverWmsPath,
                        layer: layerName,
                        visible: false,
                        legend_url: `${config.server.geoserverWmsPath}?REQUEST=GetLegendGraphic&VERSION=1.0.0&FORMAT=image/png&WIDTH=20&HEIGHT=20&LAYER=${layerName}`,
                        abstract: abstract,
                        opacity: config.layer.defaultOpacity,
                        zIndex: config.layer.defaultZIndex + index,
                    };

                    layers.push(layer);
                    console.log(`Added layer: ${layer.name} (${layer.id})`);
                } else {
                    console.log(`Skipping layer ${layerName} - doesn't match workspace`);
                }
            }
            console.log(`Total layers found: ${layers.length}`);
            return layers;
        } catch (error) {
            console.error('Error fetching layers from GeoServer:', error);
            return [];
        }
    }
}
